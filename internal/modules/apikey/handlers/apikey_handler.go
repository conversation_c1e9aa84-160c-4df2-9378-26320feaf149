package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/apikey/dto"
	_ "github.com/tranthanhloi/wn-api-v3/internal/modules/apikey/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/apikey/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/apikey/services"
	httpresponse "github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// APIKeyHandler handles API key HTTP requests
type APIKeyHandler struct {
	apiKeyService services.APIKeyService
}

// NewAPIKeyHandler creates a new API key handler
func NewAPIKeyHandler(apiKeyService services.APIKeyService) *APIKeyHandler {
	return &APIKeyHandler{
		apiKeyService: apiKeyService,
	}
}

// CreateAPIKey creates a new API key
// @Summary      Create a new API key
// @Description  Creates a new API key with the provided configuration
// @Tags         API Keys
// @Accept       json
// @Produce      json
// @Security     Bearer
// @Param        request body dto.CreateAPIKeyRequest true "API key creation data"
// @Success      201 {object} response.Response{data=dto.APIKeyResponse} "API key created successfully"
// @Failure      400 {object} response.Response "Invalid request body or missing tenant/website ID"
// @Failure      401 {object} response.Response "Authentication required"
// @Failure      500 {object} response.Response "Failed to create API key"
// @Router       /api/cms/v1/apikeys [post]
func (h *APIKeyHandler) CreateAPIKey(c *gin.Context) {
	var req dto.CreateAPIKeyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid request format")
		return
	}

	// Get tenant and website ID from context
	tenantID := getTenantIDFromContext(c)
	websiteID := getWebsiteIDFromContext(c)
	createdBy := getUserIDFromContext(c)

	if tenantID == 0 || websiteID == 0 {
		httpresponse.BadRequest(c.Writer, "Missing tenant or website ID")
		return
	}

	// Convert DTO to models
	serviceReq := &models.CreateAPIKeyRequest{
		Name:        req.Name,
		Description: req.Description,
		Permissions: req.Permissions,
		Scopes:      req.Scopes,
		IPWhitelist: req.IPWhitelist,
		RateLimit:   req.RateLimit,
		RateWindow:  req.RateWindow,
		ExpiresIn:   req.ExpiresIn,
	}

	// Create API key
	apiKey, err := h.apiKeyService.CreateAPIKey(c.Request.Context(), tenantID, websiteID, createdBy, serviceReq)
	if err != nil {
		httpresponse.InternalServerError(c.Writer, "Failed to create API key")
		return
	}

	// Convert models response to DTO response
	response := h.convertToAPIKeyResponse(apiKey)

	httpresponse.Created(c.Writer, response)
}

// GetAPIKey retrieves an API key by ID
func (h *APIKeyHandler) GetAPIKey(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid API key ID",
		})
		return
	}

	tenantID := getTenantIDFromContext(c)
	websiteID := getWebsiteIDFromContext(c)

	if tenantID == 0 || websiteID == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Missing tenant or website ID",
		})
		return
	}

	apiKey, err := h.apiKeyService.GetAPIKey(c.Request.Context(), tenantID, websiteID, uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "API key not found",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": apiKey,
	})
}

// GetAPIKeyDetail retrieves detailed API key information
func (h *APIKeyHandler) GetAPIKeyDetail(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid API key ID",
		})
		return
	}

	tenantID := getTenantIDFromContext(c)
	websiteID := getWebsiteIDFromContext(c)

	if tenantID == 0 || websiteID == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Missing tenant or website ID",
		})
		return
	}

	detail, err := h.apiKeyService.GetAPIKey(c.Request.Context(), tenantID, websiteID, uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "API key not found",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": detail,
	})
}

// ListAPIKeys retrieves a list of API keys with cursor-based pagination
func (h *APIKeyHandler) ListAPIKeys(c *gin.Context) {
	var filter dto.APIKeyFilter
	if err := c.ShouldBindQuery(&filter); err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid filter parameters")
		return
	}

	tenantID := getTenantIDFromContext(c)
	websiteID := getWebsiteIDFromContext(c)

	if tenantID == 0 || websiteID == 0 {
		httpresponse.BadRequest(c.Writer, "Missing tenant or website ID")
		return
	}

	// Create cursor request
	cursorReq := &pagination.CursorRequest{
		Cursor: filter.Cursor,
		Limit:  pagination.ValidateLimit(filter.Limit),
	}

	// Build filters map
	filters := make(map[string]interface{})
	if filter.Status != nil {
		filters["status"] = *filter.Status
	}
	if filter.Search != "" {
		filters["search"] = filter.Search
	}
	if filter.CreatedBy != nil {
		filters["created_by"] = *filter.CreatedBy
	}
	if filter.ExpiresIn != nil {
		filters["expires_in"] = *filter.ExpiresIn
	}
	if filter.SortBy != "" {
		filters["sort_by"] = filter.SortBy
	}
	if filter.SortOrder != "" {
		filters["sort_order"] = filter.SortOrder
	}

	response, err := h.apiKeyService.ListAPIKeysWithCursor(c.Request.Context(), tenantID, websiteID, cursorReq, filters)
	if err != nil {
		httpresponse.InternalServerError(c.Writer, "Failed to retrieve API keys")
		return
	}

	httpresponse.OK(c.Writer, response)
}

// UpdateAPIKey updates an existing API key
func (h *APIKeyHandler) UpdateAPIKey(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid API key ID",
		})
		return
	}

	var req models.UpdateAPIKeyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	tenantID := getTenantIDFromContext(c)
	websiteID := getWebsiteIDFromContext(c)

	if tenantID == 0 || websiteID == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Missing tenant or website ID",
		})
		return
	}

	apiKey, err := h.apiKeyService.UpdateAPIKey(c.Request.Context(), tenantID, websiteID, uint(id), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to update API key",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "API key updated successfully",
		"data":    apiKey,
	})
}

// DeleteAPIKey deletes an API key
func (h *APIKeyHandler) DeleteAPIKey(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid API key ID",
		})
		return
	}

	tenantID := getTenantIDFromContext(c)
	websiteID := getWebsiteIDFromContext(c)

	if tenantID == 0 || websiteID == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Missing tenant or website ID",
		})
		return
	}

	err = h.apiKeyService.DeleteAPIKey(c.Request.Context(), tenantID, websiteID, uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to delete API key",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "API key deleted successfully",
	})
}

// RotateAPIKey rotates an API key
func (h *APIKeyHandler) RotateAPIKey(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid API key ID",
		})
		return
	}

	var req models.RotateAPIKeyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	tenantID := getTenantIDFromContext(c)
	websiteID := getWebsiteIDFromContext(c)
	_ = getUserIDFromContext(c)

	if tenantID == 0 || websiteID == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Missing tenant or website ID",
		})
		return
	}

	response, err := h.apiKeyService.RotateAPIKey(c.Request.Context(), tenantID, websiteID, uint(id), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to rotate API key",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "API key rotated successfully",
		"data":    response,
	})
}

// RevokeAPIKey revokes an API key
func (h *APIKeyHandler) RevokeAPIKey(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid API key ID",
		})
		return
	}

	var req models.RevokeAPIKeyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	tenantID := getTenantIDFromContext(c)
	websiteID := getWebsiteIDFromContext(c)

	if tenantID == 0 || websiteID == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Missing tenant or website ID",
		})
		return
	}

	err = h.apiKeyService.RevokeAPIKey(c.Request.Context(), tenantID, websiteID, uint(id), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to revoke API key",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "API key revoked successfully",
	})
}

// ValidateAPIKey validates an API key
func (h *APIKeyHandler) ValidateAPIKey(c *gin.Context) {
	var req models.APIKeyValidationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	response, err := h.apiKeyService.ValidateAPIKey(c.Request.Context(), req.Key)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to validate API key",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": response,
	})
}

// GetAPIKeyUsage retrieves API key usage analytics
func (h *APIKeyHandler) GetAPIKeyUsage(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid API key ID",
		})
		return
	}

	var req models.APIKeyUsageRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid query parameters",
			"details": err.Error(),
		})
		return
	}

	tenantID := getTenantIDFromContext(c)
	websiteID := getWebsiteIDFromContext(c)

	if tenantID == 0 || websiteID == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Missing tenant or website ID",
		})
		return
	}

	response, err := h.apiKeyService.GetUsage(c.Request.Context(), tenantID, uint(id), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to retrieve usage data",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": response,
	})
}

// GetAPIKeyAnalytics retrieves API key analytics
func (h *APIKeyHandler) GetAPIKeyAnalytics(c *gin.Context) {
	var req models.APIKeyAnalyticsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid query parameters",
			"details": err.Error(),
		})
		return
	}

	tenantID := getTenantIDFromContext(c)
	websiteID := getWebsiteIDFromContext(c)

	if tenantID == 0 || websiteID == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Missing tenant or website ID",
		})
		return
	}

	response, err := h.apiKeyService.GetAnalytics(c.Request.Context(), tenantID, &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to retrieve analytics data",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": response,
	})
}

// BulkAction performs bulk actions on API keys
func (h *APIKeyHandler) BulkAction(c *gin.Context) {
	var req models.APIKeyBulkActionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	tenantID := getTenantIDFromContext(c)
	websiteID := getWebsiteIDFromContext(c)

	if tenantID == 0 || websiteID == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Missing tenant or website ID",
		})
		return
	}

	response, err := h.apiKeyService.BulkAction(c.Request.Context(), tenantID, websiteID, &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to perform bulk action",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Bulk action completed",
		"data":    response,
	})
}

// CreateAPIKeyPermission creates a new API key permission
func (h *APIKeyHandler) CreateAPIKeyPermission(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid API key ID",
		})
		return
	}

	var req models.CreateAPIKeyPermissionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	tenantID := getTenantIDFromContext(c)

	if tenantID == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Missing tenant ID",
		})
		return
	}

	permission, err := h.apiKeyService.CreatePermission(c.Request.Context(), tenantID, uint(id), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to create permission",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "Permission created successfully",
		"data":    permission,
	})
}

// GetAPIKeyPermissions retrieves API key permissions
func (h *APIKeyHandler) GetAPIKeyPermissions(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid API key ID",
		})
		return
	}

	tenantID := getTenantIDFromContext(c)

	if tenantID == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Missing tenant ID",
		})
		return
	}

	permissions, err := h.apiKeyService.GetPermissions(c.Request.Context(), tenantID, uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to retrieve permissions",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": permissions,
	})
}

// DeleteAPIKeyPermission deletes an API key permission
func (h *APIKeyHandler) DeleteAPIKeyPermission(c *gin.Context) {
	permissionID, err := strconv.ParseUint(c.Param("permission_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid permission ID",
		})
		return
	}

	tenantID := getTenantIDFromContext(c)

	if tenantID == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Missing tenant ID",
		})
		return
	}

	err = h.apiKeyService.DeletePermission(c.Request.Context(), tenantID, uint(permissionID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to delete permission",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Permission deleted successfully",
	})
}

// Helper functions to get context values
func getTenantIDFromContext(c *gin.Context) uint {
	if tenantID, exists := c.Get("tenant_id"); exists {
		if id, ok := tenantID.(uint); ok {
			return id
		}
	}
	return 0
}

func getWebsiteIDFromContext(c *gin.Context) uint {
	if websiteID, exists := c.Get("website_id"); exists {
		if id, ok := websiteID.(uint); ok {
			return id
		}
	}
	return 0
}

func getUserIDFromContext(c *gin.Context) uint {
	if userID, exists := c.Get("user_id"); exists {
		if id, ok := userID.(uint); ok {
			return id
		}
	}
	return 0
}

// Conversion functions
func (h *APIKeyHandler) convertToAPIKeyResponse(apiKey *models.APIKeyResponse) *dto.APIKeyResponse {
	return &dto.APIKeyResponse{
		ID:          apiKey.ID,
		TenantID:    apiKey.TenantID,
		WebsiteID:   apiKey.WebsiteID,
		Key:         apiKey.Key,
		KeyPrefix:   apiKey.KeyPrefix,
		Name:        apiKey.Name,
		Description: apiKey.Description,
		Status:      apiKey.Status,
		RateLimit:   apiKey.RateLimit,
		RateWindow:  apiKey.RateWindow,
		ExpiresAt:   apiKey.ExpiresAt,
		LastUsedAt:  apiKey.LastUsedAt,
		CreatedAt:   apiKey.CreatedAt,
		UpdatedAt:   apiKey.UpdatedAt,
	}
}
