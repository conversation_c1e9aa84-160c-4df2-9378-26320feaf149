package models

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"os"
	"strconv"
	"time"

	"gorm.io/gorm"
)

// Setting represents a hierarchical configuration setting
type Setting struct {
	ID          uint            `json:"id" gorm:"primaryKey"`
	ScopeType   ScopeType       `json:"scope_type" gorm:"type:enum('global','tenant','website','user');not null;index:idx_setting_scope;uniqueIndex:idx_setting_scope_category_key"`
	ScopeID     *uint           `json:"scope_id,omitempty" gorm:"index:idx_setting_scope;uniqueIndex:idx_setting_scope_category_key"`
	Category    string          `json:"category" gorm:"type:varchar(100);not null;uniqueIndex:idx_setting_scope_category_key"`
	Key         string          `json:"key" gorm:"type:varchar(255);not null;uniqueIndex:idx_setting_scope_category_key"`
	Value       json.RawMessage `json:"value" gorm:"type:json;not null"`
	Description string          `json:"description,omitempty" gorm:"type:text"`
	DataType    DataType        `json:"data_type" gorm:"type:enum('string','number','boolean','json','array');not null;default:'string'"`
	IsEncrypted bool            `json:"is_encrypted" gorm:"not null;default:false"`
	IsPublic    bool            `json:"is_public" gorm:"not null;default:false"`
	IsRequired  bool            `json:"is_required" gorm:"not null;default:false"`
	IsReadOnly  bool            `json:"is_read_only" gorm:"not null;default:false"`

	// Validation and defaults
	ValidationRules json.RawMessage `json:"validation_rules,omitempty" gorm:"type:json"`
	DefaultValue    json.RawMessage `json:"default_value,omitempty" gorm:"type:json"`
	Options         json.RawMessage `json:"options,omitempty" gorm:"type:json"`

	// Metadata
	CreatedBy *uint     `json:"created_by,omitempty"`
	UpdatedBy *uint     `json:"updated_by,omitempty"`
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}

// TableName specifies the table name for Setting
func (Setting) TableName() string {
	return "settings"
}

// BeforeCreate hook for GORM
func (s *Setting) BeforeCreate(tx *gorm.DB) error {
	return s.encryptValue()
}

// BeforeUpdate hook for GORM
func (s *Setting) BeforeUpdate(tx *gorm.DB) error {
	return s.encryptValue()
}

// AfterFind hook for GORM
func (s *Setting) AfterFind(tx *gorm.DB) error {
	return s.decryptValue()
}

// GetValueAsString returns the value as string
func (s *Setting) GetValueAsString() (string, error) {
	var value string
	err := json.Unmarshal(s.Value, &value)
	return value, err
}

// GetValueAsInt returns the value as int
func (s *Setting) GetValueAsInt() (int, error) {
	var value int
	err := json.Unmarshal(s.Value, &value)
	return value, err
}

// GetValueAsFloat returns the value as float64
func (s *Setting) GetValueAsFloat() (float64, error) {
	var value float64
	err := json.Unmarshal(s.Value, &value)
	return value, err
}

// GetValueAsBool returns the value as bool
func (s *Setting) GetValueAsBool() (bool, error) {
	var value bool
	err := json.Unmarshal(s.Value, &value)
	return value, err
}

// GetValueAsJSON returns the value as a map
func (s *Setting) GetValueAsJSON() (map[string]interface{}, error) {
	var value map[string]interface{}
	err := json.Unmarshal(s.Value, &value)
	return value, err
}

// GetValueAsArray returns the value as a slice
func (s *Setting) GetValueAsArray() ([]interface{}, error) {
	var value []interface{}
	err := json.Unmarshal(s.Value, &value)
	return value, err
}

// SetValue sets the value with proper JSON encoding
func (s *Setting) SetValue(value interface{}) error {
	bytes, err := json.Marshal(value)
	if err != nil {
		return err
	}
	s.Value = bytes
	return nil
}

// GetTypedValue returns the value based on the data type
func (s *Setting) GetTypedValue() (interface{}, error) {
	switch s.DataType {
	case DataTypeString:
		return s.GetValueAsString()
	case DataTypeNumber:
		// Try int first, then float
		if intVal, err := s.GetValueAsInt(); err == nil {
			return intVal, nil
		}
		return s.GetValueAsFloat()
	case DataTypeBoolean:
		return s.GetValueAsBool()
	case DataTypeJSON:
		return s.GetValueAsJSON()
	case DataTypeArray:
		return s.GetValueAsArray()
	default:
		return s.GetValueAsString()
	}
}

// ValidateValue validates the setting value against the validation rules
func (s *Setting) ValidateValue() error {
	if s.ValidationRules == nil {
		return nil
	}

	var rules map[string]interface{}
	if err := json.Unmarshal(s.ValidationRules, &rules); err != nil {
		return fmt.Errorf("invalid validation rules: %w", err)
	}

	value, err := s.GetTypedValue()
	if err != nil {
		return fmt.Errorf("invalid value: %w", err)
	}

	return s.validateAgainstRules(value, rules)
}

// validateAgainstRules validates a value against the given rules
func (s *Setting) validateAgainstRules(value interface{}, rules map[string]interface{}) error {
	// Required validation
	if required, ok := rules["required"].(bool); ok && required {
		if value == nil || value == "" {
			return errors.New("value is required")
		}
	}

	// String validations
	if strVal, ok := value.(string); ok {
		if minLen, ok := rules["min_length"].(float64); ok {
			if len(strVal) < int(minLen) {
				return fmt.Errorf("value must be at least %d characters", int(minLen))
			}
		}
		if maxLen, ok := rules["max_length"].(float64); ok {
			if len(strVal) > int(maxLen) {
				return fmt.Errorf("value must be at most %d characters", int(maxLen))
			}
		}
		if regex, ok := rules["regex"].(string); ok {
			// TODO: Implement regex validation
			_ = regex
		}
	}

	// Number validations
	if numVal, ok := value.(float64); ok {
		if min, ok := rules["min"].(float64); ok {
			if numVal < min {
				return fmt.Errorf("value must be at least %f", min)
			}
		}
		if max, ok := rules["max"].(float64); ok {
			if numVal > max {
				return fmt.Errorf("value must be at most %f", max)
			}
		}
	}

	// Enum validation
	if enumValues, ok := rules["enum"].([]interface{}); ok {
		found := false
		for _, enumVal := range enumValues {
			if value == enumVal {
				found = true
				break
			}
		}
		if !found {
			return fmt.Errorf("value must be one of: %v", enumValues)
		}
	}

	return nil
}

// encryptValue encrypts the value if encryption is enabled
func (s *Setting) encryptValue() error {
	if !s.IsEncrypted {
		return nil
	}

	key := os.Getenv("SETTINGS_ENCRYPTION_KEY")
	if key == "" {
		return errors.New("encryption key not configured")
	}

	if len(key) != 32 {
		return errors.New("encryption key must be 32 bytes")
	}

	block, err := aes.NewCipher([]byte(key))
	if err != nil {
		return err
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return err
	}

	nonce := make([]byte, gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return err
	}

	ciphertext := gcm.Seal(nonce, nonce, s.Value, nil)
	s.Value = json.RawMessage(fmt.Sprintf(`"%s"`, base64.StdEncoding.EncodeToString(ciphertext)))

	return nil
}

// decryptValue decrypts the value if encryption is enabled
func (s *Setting) decryptValue() error {
	if !s.IsEncrypted {
		return nil
	}

	key := os.Getenv("SETTINGS_ENCRYPTION_KEY")
	if key == "" {
		return errors.New("encryption key not configured")
	}

	if len(key) != 32 {
		return errors.New("encryption key must be 32 bytes")
	}

	var encryptedStr string
	if err := json.Unmarshal(s.Value, &encryptedStr); err != nil {
		return err
	}

	ciphertext, err := base64.StdEncoding.DecodeString(encryptedStr)
	if err != nil {
		return err
	}

	block, err := aes.NewCipher([]byte(key))
	if err != nil {
		return err
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return err
	}

	nonceSize := gcm.NonceSize()
	if len(ciphertext) < nonceSize {
		return errors.New("ciphertext too short")
	}

	nonce, ciphertext := ciphertext[:nonceSize], ciphertext[nonceSize:]
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return err
	}

	s.Value = json.RawMessage(plaintext)
	return nil
}

// GetScopeKey returns a unique key for the setting scope
func (s *Setting) GetScopeKey() string {
	scopeID := "global"
	if s.ScopeID != nil {
		scopeID = strconv.FormatUint(uint64(*s.ScopeID), 10)
	}
	return fmt.Sprintf("%s:%s", s.ScopeType, scopeID)
}

// GetFullKey returns the full key including scope, category, and key
func (s *Setting) GetFullKey() string {
	return fmt.Sprintf("%s:%s:%s", s.GetScopeKey(), s.Category, s.Key)
}

// IsGlobal returns true if the setting is global
func (s *Setting) IsGlobal() bool {
	return s.ScopeType == ScopeTypeGlobal
}

// IsTenant returns true if the setting is tenant-scoped
func (s *Setting) IsTenant() bool {
	return s.ScopeType == ScopeTypeTenant
}

// IsWebsite returns true if the setting is website-scoped
func (s *Setting) IsWebsite() bool {
	return s.ScopeType == ScopeTypeWebsite
}

// IsUser returns true if the setting is user-scoped
func (s *Setting) IsUser() bool {
	return s.ScopeType == ScopeTypeUser
}
