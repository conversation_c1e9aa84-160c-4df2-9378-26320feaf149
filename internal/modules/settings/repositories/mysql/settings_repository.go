package mysql

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/settings/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/settings/repositories"
)

type settingsRepository struct {
	db      *gorm.DB
	options *repositories.SettingsRepositoryOptions
}

// NewSettingsRepository creates a new MySQL settings repository
func NewSettingsRepository(db *gorm.DB, options *repositories.SettingsRepositoryOptions) repositories.SettingsRepository {
	if options == nil {
		options = &repositories.SettingsRepositoryOptions{
			EnableCache:      false,
			CacheTimeout:     300,
			CachePrefix:      "settings:",
			EnableHistory:    false,
			HistoryLimit:     100,
			EnableEncryption: false,
		}
	}
	return &settingsRepository{
		db:      db,
		options: options,
	}
}

// Create creates a new setting
func (r *settingsRepository) Create(ctx context.Context, setting *models.Setting) error {
	if err := setting.Validate(); err != nil {
		return fmt.Errorf("setting validation failed: %w", err)
	}

	if err := r.db.WithContext(ctx).Create(setting).Error; err != nil {
		return fmt.Errorf("failed to create setting: %w", err)
	}

	// Create history record if enabled
	if r.options.EnableHistory {
		history := &models.SettingHistory{
			SettingID: setting.ID,
			Key:       setting.Key,
			NewValue:  setting.Value,
			Action:    "create",
			Version:   setting.Version,
			ChangedBy: setting.CreatedBy,
		}
		_ = r.CreateHistory(ctx, history)
	}

	return nil
}

// GetByID retrieves a setting by ID
func (r *settingsRepository) GetByID(ctx context.Context, id uint) (*models.Setting, error) {
	var setting models.Setting
	err := r.db.WithContext(ctx).
		Where("id = ? AND status != ?", id, models.SettingStatusDeleted).
		First(&setting).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get setting by ID: %w", err)
	}

	return &setting, nil
}

// GetByKey retrieves a setting by key, scope, and scope ID
func (r *settingsRepository) GetByKey(ctx context.Context, key string, scope models.ScopeType, scopeID uint) (*models.Setting, error) {
	var setting models.Setting
	query := r.db.WithContext(ctx).
		Where("key = ? AND scope = ? AND status != ?", key, scope, models.SettingStatusDeleted)

	if scopeID > 0 {
		query = query.Where("scope_id = ?", scopeID)
	} else {
		query = query.Where("scope_id IS NULL OR scope_id = 0")
	}

	err := query.First(&setting).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get setting by key: %w", err)
	}

	return &setting, nil
}

// Update updates a setting
func (r *settingsRepository) Update(ctx context.Context, id uint, updates map[string]interface{}) error {
	// Get current setting for history
	var oldSetting models.Setting
	if r.options.EnableHistory {
		if err := r.db.WithContext(ctx).First(&oldSetting, id).Error; err != nil {
			return fmt.Errorf("failed to get current setting for history: %w", err)
		}
	}

	// Remove fields that shouldn't be updated directly
	delete(updates, "id")
	delete(updates, "key")
	delete(updates, "scope")
	delete(updates, "scope_id")
	delete(updates, "created_at")
	delete(updates, "created_by")

	// Increment version if value is updated
	if _, valueUpdated := updates["value"]; valueUpdated {
		updates["version"] = gorm.Expr("version + 1")
	}

	result := r.db.WithContext(ctx).
		Model(&models.Setting{}).
		Where("id = ?", id).
		Updates(updates)

	if result.Error != nil {
		return fmt.Errorf("failed to update setting: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("setting not found")
	}

	// Create history record if enabled
	if r.options.EnableHistory {
		var newSetting models.Setting
		if err := r.db.WithContext(ctx).First(&newSetting, id).Error; err == nil {
			history := &models.SettingHistory{
				SettingID: id,
				Key:       oldSetting.Key,
				OldValue:  oldSetting.Value,
				NewValue:  newSetting.Value,
				Action:    "update",
				Version:   newSetting.Version,
				ChangedBy: newSetting.UpdatedBy,
			}
			_ = r.CreateHistory(ctx, history)
		}
	}

	return nil
}

// Delete permanently deletes a setting
func (r *settingsRepository) Delete(ctx context.Context, id uint) error {
	result := r.db.WithContext(ctx).Delete(&models.Setting{}, id)
	if result.Error != nil {
		return fmt.Errorf("failed to delete setting: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("setting not found")
	}
	return nil
}

// SoftDelete soft deletes a setting by setting status to deleted
func (r *settingsRepository) SoftDelete(ctx context.Context, id uint) error {
	result := r.db.WithContext(ctx).
		Model(&models.Setting{}).
		Where("id = ?", id).
		Update("status", models.SettingStatusDeleted)

	if result.Error != nil {
		return fmt.Errorf("failed to soft delete setting: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("setting not found")
	}

	return nil
}

// SoftDeleteByKey soft deletes a setting by key
func (r *settingsRepository) SoftDeleteByKey(ctx context.Context, key string, scope models.ScopeType, scopeID uint) error {
	query := r.db.WithContext(ctx).
		Model(&models.Setting{}).
		Where("key = ? AND scope = ?", key, scope)

	if scopeID > 0 {
		query = query.Where("scope_id = ?", scopeID)
	} else {
		query = query.Where("scope_id IS NULL OR scope_id = 0")
	}

	result := query.Update("status", models.SettingStatusDeleted)

	if result.Error != nil {
		return fmt.Errorf("failed to soft delete setting by key: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("setting not found")
	}

	return nil
}

// Restore restores a soft deleted setting
func (r *settingsRepository) Restore(ctx context.Context, id uint) error {
	result := r.db.WithContext(ctx).
		Model(&models.Setting{}).
		Where("id = ? AND status = ?", id, models.SettingStatusDeleted).
		Update("status", models.SettingStatusActive)

	if result.Error != nil {
		return fmt.Errorf("failed to restore setting: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("setting not found or not deleted")
	}

	return nil
}

// Upsert creates or updates a setting
func (r *settingsRepository) Upsert(ctx context.Context, setting *models.Setting) error {
	if err := setting.Validate(); err != nil {
		return fmt.Errorf("setting validation failed: %w", err)
	}

	// Build conflict columns based on unique constraint
	conflictColumns := []clause.Column{
		{Name: "key"},
		{Name: "scope"},
		{Name: "scope_id"},
	}

	// Fields to update on conflict
	updateFields := []string{
		"value",
		"description",
		"data_type",
		"is_encrypted",
		"is_public",
		"is_editable",
		"is_required",
		"status",
		"schema_id",
		"updated_by",
		"updated_at",
	}

	return r.db.WithContext(ctx).
		Clauses(clause.OnConflict{
			Columns:   conflictColumns,
			DoUpdates: clause.AssignmentColumns(updateFields),
		}).
		Create(setting).Error
}

// UpsertByKey creates or updates a setting by key
func (r *settingsRepository) UpsertByKey(ctx context.Context, key string, scope models.ScopeType, scopeID uint, value interface{}) error {
	valueBytes, err := json.Marshal(value)
	if err != nil {
		return fmt.Errorf("failed to marshal value: %w", err)
	}

	setting := &models.Setting{
		Key:     key,
		Value:   json.RawMessage(valueBytes),
		Scope:   scope,
		ScopeID: scopeID,
		Status:  models.SettingStatusActive,
		Version: 1,
	}

	return r.Upsert(ctx, setting)
}

// BulkCreate creates multiple settings
func (r *settingsRepository) BulkCreate(ctx context.Context, settings []*models.Setting) error {
	if len(settings) == 0 {
		return nil
	}

	// Validate all settings
	for _, setting := range settings {
		if err := setting.Validate(); err != nil {
			return fmt.Errorf("setting validation failed for key '%s': %w", setting.Key, err)
		}
	}

	return r.db.WithContext(ctx).CreateInBatches(settings, 100).Error
}

// BulkUpdate updates multiple settings
func (r *settingsRepository) BulkUpdate(ctx context.Context, settings []models.BulkSettingItem) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, item := range settings {
			valueBytes, err := json.Marshal(item.Value)
			if err != nil {
				return fmt.Errorf("failed to marshal value for key '%s': %w", item.Key, err)
			}

			query := tx.Model(&models.Setting{}).
				Where("key = ? AND scope = ?", item.Key, item.Scope)

			if item.ScopeID > 0 {
				query = query.Where("scope_id = ?", item.ScopeID)
			} else {
				query = query.Where("scope_id IS NULL OR scope_id = 0")
			}

			if err := query.Updates(map[string]interface{}{
				"value":      json.RawMessage(valueBytes),
				"category":   item.Category,
				"version":    gorm.Expr("version + 1"),
				"updated_at": time.Now(),
			}).Error; err != nil {
				return fmt.Errorf("failed to update setting '%s': %w", item.Key, err)
			}
		}
		return nil
	})
}

// BulkUpsert creates or updates multiple settings
func (r *settingsRepository) BulkUpsert(ctx context.Context, settings []models.BulkSettingItem) error {
	if len(settings) == 0 {
		return nil
	}

	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, item := range settings {
			valueBytes, err := json.Marshal(item.Value)
			if err != nil {
				return fmt.Errorf("failed to marshal value for key '%s': %w", item.Key, err)
			}

			setting := &models.Setting{
				Key:      item.Key,
				Value:    json.RawMessage(valueBytes),
				Category: item.Category,
				Scope:    item.Scope,
				ScopeID:  item.ScopeID,
				Status:   models.SettingStatusActive,
				Version:  1,
			}

			if err := r.Upsert(ctx, setting); err != nil {
				return fmt.Errorf("failed to upsert setting '%s': %w", item.Key, err)
			}
		}
		return nil
	})
}

// BulkDelete deletes multiple settings
func (r *settingsRepository) BulkDelete(ctx context.Context, keys []string, scope models.ScopeType, scopeID uint) error {
	if len(keys) == 0 {
		return nil
	}

	query := r.db.WithContext(ctx).
		Model(&models.Setting{}).
		Where("key IN ? AND scope = ?", keys, scope)

	if scopeID > 0 {
		query = query.Where("scope_id = ?", scopeID)
	} else {
		query = query.Where("scope_id IS NULL OR scope_id = 0")
	}

	return query.Update("status", models.SettingStatusDeleted).Error
}

// List retrieves settings based on filter
func (r *settingsRepository) List(ctx context.Context, filter models.SettingFilter) ([]*models.Setting, error) {
	query := r.buildQuery(ctx, filter)

	var settings []*models.Setting
	if err := query.Find(&settings).Error; err != nil {
		return nil, fmt.Errorf("failed to list settings: %w", err)
	}

	return settings, nil
}

// ListWithCount retrieves settings with count based on filter
func (r *settingsRepository) ListWithCount(ctx context.Context, filter models.SettingFilter) ([]*models.Setting, int64, error) {
	query := r.buildQuery(ctx, filter)

	var count int64
	if err := query.Count(&count).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count settings: %w", err)
	}

	// Apply pagination
	if filter.Limit > 0 {
		query = query.Limit(filter.Limit)
	}
	if filter.Offset > 0 {
		query = query.Offset(filter.Offset)
	}

	var settings []*models.Setting
	if err := query.Find(&settings).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to list settings: %w", err)
	}

	return settings, count, nil
}

// buildQuery builds the GORM query based on filter
func (r *settingsRepository) buildQuery(ctx context.Context, filter models.SettingFilter) *gorm.DB {
	query := r.db.WithContext(ctx).Model(&models.Setting{})

	// Default filter for non-deleted records
	if filter.Status == "" {
		query = query.Where("status != ?", models.SettingStatusDeleted)
	}

	// Apply filters
	if filter.Key != "" {
		query = query.Where("key = ?", filter.Key)
	}

	if len(filter.Keys) > 0 {
		query = query.Where("key IN ?", filter.Keys)
	}

	if filter.Category != "" {
		query = query.Where("category = ?", filter.Category)
	}

	if len(filter.Categories) > 0 {
		query = query.Where("category IN ?", filter.Categories)
	}

	if filter.Scope != "" {
		query = query.Where("scope = ?", filter.Scope)
	}

	if filter.ScopeID > 0 {
		query = query.Where("scope_id = ?", filter.ScopeID)
	}

	if filter.DataType != "" {
		query = query.Where("data_type = ?", filter.DataType)
	}

	if filter.IsPublic != nil {
		query = query.Where("is_public = ?", *filter.IsPublic)
	}

	if filter.IsEditable != nil {
		query = query.Where("is_editable = ?", *filter.IsEditable)
	}

	if filter.IsRequired != nil {
		query = query.Where("is_required = ?", *filter.IsRequired)
	}

	if filter.Status != "" {
		query = query.Where("status = ?", filter.Status)
	}

	if filter.SchemaID > 0 {
		query = query.Where("schema_id = ?", filter.SchemaID)
	}

	if filter.CreatedBy > 0 {
		query = query.Where("created_by = ?", filter.CreatedBy)
	}

	if filter.UpdatedBy > 0 {
		query = query.Where("updated_by = ?", filter.UpdatedBy)
	}

	if filter.Search != "" {
		search := "%" + filter.Search + "%"
		query = query.Where("key LIKE ? OR description LIKE ?", search, search)
	}

	// Default ordering
	query = query.Order("category ASC, key ASC")

	return query
}

// GetByCategory retrieves settings by category
func (r *settingsRepository) GetByCategory(ctx context.Context, category string, scope models.ScopeType, scopeID uint) ([]*models.Setting, error) {
	filter := models.SettingFilter{
		Category: category,
		Scope:    scope,
		ScopeID:  scopeID,
	}
	return r.List(ctx, filter)
}

// GetByScope retrieves settings by scope
func (r *settingsRepository) GetByScope(ctx context.Context, scope models.ScopeType, scopeID uint) ([]*models.Setting, error) {
	filter := models.SettingFilter{
		Scope:   scope,
		ScopeID: scopeID,
	}
	return r.List(ctx, filter)
}

// GetByKeys retrieves settings by multiple keys
func (r *settingsRepository) GetByKeys(ctx context.Context, keys []string, scope models.ScopeType, scopeID uint) ([]*models.Setting, error) {
	filter := models.SettingFilter{
		Keys:    keys,
		Scope:   scope,
		ScopeID: scopeID,
	}
	return r.List(ctx, filter)
}

// GetHierarchical retrieves a setting using hierarchical resolution
func (r *settingsRepository) GetHierarchical(ctx context.Context, key string, scopes []models.Scope) (*models.Setting, error) {
	// Try each scope in order until we find a setting
	for _, scope := range scopes {
		setting, err := r.GetByKey(ctx, key, scope.Type, scope.ID)
		if err != nil {
			return nil, err
		}
		if setting != nil {
			return setting, nil
		}
	}
	return nil, nil
}

// GetHierarchicalMultiple retrieves multiple settings using hierarchical resolution
func (r *settingsRepository) GetHierarchicalMultiple(ctx context.Context, keys []string, scopes []models.Scope) ([]*models.Setting, error) {
	var result []*models.Setting
	settingMap := make(map[string]*models.Setting)

	// For each scope, try to get all requested keys
	for _, scope := range scopes {
		remainingKeys := make([]string, 0)
		for _, key := range keys {
			if _, found := settingMap[key]; !found {
				remainingKeys = append(remainingKeys, key)
			}
		}

		if len(remainingKeys) == 0 {
			break
		}

		settings, err := r.GetByKeys(ctx, remainingKeys, scope.Type, scope.ID)
		if err != nil {
			return nil, err
		}

		for _, setting := range settings {
			if _, found := settingMap[setting.Key]; !found {
				settingMap[setting.Key] = setting
				result = append(result, setting)
			}
		}
	}

	return result, nil
}

// ResolveHierarchical resolves a setting value using hierarchical resolution
func (r *settingsRepository) ResolveHierarchical(ctx context.Context, key string, scopes []models.Scope) (interface{}, error) {
	setting, err := r.GetHierarchical(ctx, key, scopes)
	if err != nil {
		return nil, err
	}

	if setting == nil {
		return nil, nil
	}

	// Try to unmarshal the value
	var value interface{}
	if err := json.Unmarshal(setting.Value, &value); err != nil {
		return nil, fmt.Errorf("failed to unmarshal setting value: %w", err)
	}

	return value, nil
}

// Placeholder implementations for remaining methods to satisfy interface
// These would need to be implemented based on specific caching and other requirements

func (r *settingsRepository) ClearCache(ctx context.Context, key string, scope models.ScopeType, scopeID uint) error {
	// Cache implementation would go here
	return nil
}

func (r *settingsRepository) ClearCacheByCategory(ctx context.Context, category string, scope models.ScopeType, scopeID uint) error {
	// Cache implementation would go here
	return nil
}

func (r *settingsRepository) ClearCacheByScope(ctx context.Context, scope models.ScopeType, scopeID uint) error {
	// Cache implementation would go here
	return nil
}

func (r *settingsRepository) RefreshCache(ctx context.Context, key string, scope models.ScopeType, scopeID uint) error {
	// Cache implementation would go here
	return nil
}

func (r *settingsRepository) ValidateSettings(ctx context.Context, settings []models.BulkSettingItem) ([]repositories.ValidationError, error) {
	var errors []repositories.ValidationError

	for _, item := range settings {
		setting := &models.Setting{
			Key:      item.Key,
			Category: item.Category,
			Scope:    item.Scope,
			ScopeID:  item.ScopeID,
		}

		if err := setting.SetValue(item.Value); err != nil {
			errors = append(errors, repositories.ValidationError{
				Key:     item.Key,
				Message: fmt.Sprintf("Invalid value: %v", err),
				Field:   "value",
			})
			continue
		}

		if err := setting.Validate(); err != nil {
			errors = append(errors, repositories.ValidationError{
				Key:     item.Key,
				Message: err.Error(),
			})
		}
	}

	return errors, nil
}

func (r *settingsRepository) ValidateValue(ctx context.Context, key string, value interface{}) error {
	setting := &models.Setting{Key: key}
	if err := setting.SetValue(value); err != nil {
		return err
	}
	return setting.Validate()
}

func (r *settingsRepository) Search(ctx context.Context, query string, filter models.SettingFilter) ([]*models.Setting, error) {
	filter.Search = query
	return r.List(ctx, filter)
}

func (r *settingsRepository) SearchByValue(ctx context.Context, value interface{}, filter models.SettingFilter) ([]*models.Setting, error) {
	valueBytes, err := json.Marshal(value)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal search value: %w", err)
	}

	query := r.buildQuery(ctx, filter)
	query = query.Where("value = ?", string(valueBytes))

	var settings []*models.Setting
	if err := query.Find(&settings).Error; err != nil {
		return nil, fmt.Errorf("failed to search settings by value: %w", err)
	}

	return settings, nil
}

func (r *settingsRepository) CountByScope(ctx context.Context, scope models.ScopeType, scopeID uint) (int64, error) {
	var count int64
	query := r.db.WithContext(ctx).Model(&models.Setting{}).
		Where("scope = ? AND status != ?", scope, models.SettingStatusDeleted)

	if scopeID > 0 {
		query = query.Where("scope_id = ?", scopeID)
	}

	if err := query.Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count settings by scope: %w", err)
	}

	return count, nil
}

func (r *settingsRepository) CountByCategory(ctx context.Context, category string, scope models.ScopeType, scopeID uint) (int64, error) {
	var count int64
	query := r.db.WithContext(ctx).Model(&models.Setting{}).
		Where("category = ? AND scope = ? AND status != ?", category, scope, models.SettingStatusDeleted)

	if scopeID > 0 {
		query = query.Where("scope_id = ?", scopeID)
	}

	if err := query.Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count settings by category: %w", err)
	}

	return count, nil
}

func (r *settingsRepository) CountByStatus(ctx context.Context, status models.SettingStatus) (int64, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&models.Setting{}).
		Where("status = ?", status).
		Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count settings by status: %w", err)
	}
	return count, nil
}

func (r *settingsRepository) Export(ctx context.Context, filter models.SettingFilter) ([]*models.Setting, error) {
	return r.List(ctx, filter)
}

func (r *settingsRepository) Import(ctx context.Context, settings []*models.Setting) error {
	return r.BulkCreate(ctx, settings)
}

func (r *settingsRepository) GetHistory(ctx context.Context, settingID uint, limit int, offset int) ([]*models.SettingHistory, error) {
	var histories []*models.SettingHistory
	query := r.db.WithContext(ctx).
		Where("setting_id = ?", settingID).
		Order("changed_at DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}
	if offset > 0 {
		query = query.Offset(offset)
	}

	if err := query.Find(&histories).Error; err != nil {
		return nil, fmt.Errorf("failed to get setting history: %w", err)
	}

	return histories, nil
}

func (r *settingsRepository) CreateHistory(ctx context.Context, history *models.SettingHistory) error {
	return r.db.WithContext(ctx).Create(history).Error
}

func (r *settingsRepository) Exists(ctx context.Context, key string, scope models.ScopeType, scopeID uint) (bool, error) {
	var count int64
	query := r.db.WithContext(ctx).Model(&models.Setting{}).
		Where("key = ? AND scope = ? AND status != ?", key, scope, models.SettingStatusDeleted)

	if scopeID > 0 {
		query = query.Where("scope_id = ?", scopeID)
	}

	if err := query.Count(&count).Error; err != nil {
		return false, fmt.Errorf("failed to check setting existence: %w", err)
	}

	return count > 0, nil
}

func (r *settingsRepository) ExistsMultiple(ctx context.Context, keys []string, scope models.ScopeType, scopeID uint) (map[string]bool, error) {
	result := make(map[string]bool)

	for _, key := range keys {
		exists, err := r.Exists(ctx, key, scope, scopeID)
		if err != nil {
			return nil, err
		}
		result[key] = exists
	}

	return result, nil
}

func (r *settingsRepository) GetConfiguration(ctx context.Context, category string, scope models.ScopeType, scopeID uint) (map[string]interface{}, error) {
	settings, err := r.GetByCategory(ctx, category, scope, scopeID)
	if err != nil {
		return nil, err
	}

	config := make(map[string]interface{})
	for _, setting := range settings {
		var value interface{}
		if err := json.Unmarshal(setting.Value, &value); err != nil {
			return nil, fmt.Errorf("failed to unmarshal setting value for key '%s': %w", setting.Key, err)
		}
		config[setting.Key] = value
	}

	return config, nil
}

func (r *settingsRepository) SetConfiguration(ctx context.Context, category string, scope models.ScopeType, scopeID uint, config map[string]interface{}) error {
	var bulkItems []models.BulkSettingItem

	for key, value := range config {
		bulkItems = append(bulkItems, models.BulkSettingItem{
			Key:      key,
			Value:    value,
			Category: category,
			Scope:    scope,
			ScopeID:  scopeID,
		})
	}

	return r.BulkUpsert(ctx, bulkItems)
}

func (r *settingsRepository) Cleanup(ctx context.Context, daysOld int) (int64, error) {
	cutoffTime := time.Now().AddDate(0, 0, -daysOld)

	result := r.db.WithContext(ctx).
		Where("status = ? AND updated_at < ?", models.SettingStatusDeleted, cutoffTime).
		Delete(&models.Setting{})

	if result.Error != nil {
		return 0, fmt.Errorf("failed to cleanup old settings: %w", result.Error)
	}

	return result.RowsAffected, nil
}

func (r *settingsRepository) Optimize(ctx context.Context) error {
	// This would include database optimization tasks
	// For now, just a placeholder
	return nil
}

func (r *settingsRepository) GetPublicSettings(ctx context.Context, scope models.ScopeType, scopeID uint) ([]*models.Setting, error) {
	filter := models.SettingFilter{
		Scope:   scope,
		ScopeID: scopeID,
	}
	isPublic := true
	filter.IsPublic = &isPublic

	return r.List(ctx, filter)
}

func (r *settingsRepository) GetEditableSettings(ctx context.Context, scope models.ScopeType, scopeID uint) ([]*models.Setting, error) {
	filter := models.SettingFilter{
		Scope:   scope,
		ScopeID: scopeID,
	}
	isEditable := true
	filter.IsEditable = &isEditable

	return r.List(ctx, filter)
}

func (r *settingsRepository) CheckPermission(ctx context.Context, key string, scope models.ScopeType, scopeID uint, userID uint) (bool, error) {
	// Permission checking logic would go here
	// For now, just return true
	return true, nil
}
