package repositories

import (
	"context"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/settings/models"
)

// SettingsRepository defines the interface for settings data operations
type SettingsRepository interface {
	// Basic CRUD operations
	CreateSetting(ctx context.Context, tenantID uint, setting *models.Setting) (*models.Setting, error)
	GetSettingByID(ctx context.Context, tenantID, settingID uint) (*models.Setting, error)
	GetSettingByKey(ctx context.Context, tenantID uint, scopeType models.ScopeType, scopeID *uint, category, key string) (*models.Setting, error)
	UpdateSetting(ctx context.Context, tenantID uint, setting *models.Setting) (*models.Setting, error)
	DeleteSetting(ctx context.Context, tenantID, settingID uint) error

	// Query operations
	GetSettings(ctx context.Context, tenantID uint, filter models.SettingFilter) ([]*models.Setting, error)
	GetSettingsByCategory(ctx context.Context, tenantID uint, scopeType models.ScopeType, scopeID *uint, category string) ([]*models.Setting, error)

	// Existence check
	SettingExists(ctx context.Context, tenantID uint, scopeType models.ScopeType, scopeID *uint, category, key string) (bool, error)
}

// ValidationError represents a validation error for a setting
type ValidationError struct {
	Key     string `json:"key"`
	Message string `json:"message"`
	Field   string `json:"field,omitempty"`
}

// SettingsRepositoryOptions represents options for the settings repository
type SettingsRepositoryOptions struct {
	EnableCache      bool   `json:"enable_cache"`
	CacheTimeout     int    `json:"cache_timeout"`
	CachePrefix      string `json:"cache_prefix"`
	EnableHistory    bool   `json:"enable_history"`
	HistoryLimit     int    `json:"history_limit"`
	EnableEncryption bool   `json:"enable_encryption"`
	EncryptionKey    string `json:"encryption_key"`
}
